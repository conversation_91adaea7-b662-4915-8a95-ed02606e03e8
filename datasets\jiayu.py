import os
import json
import numpy as np
from PIL import Image
import torch
from torch.utils import data
from datasets import utils


class JiayuDataset(data.Dataset):
    """Jiayu Dataset for semantic segmentation
    
    A Unity-generated dataset containing rock segmentation data.
    Dataset contains 197 samples with RGB images and semantic segmentation masks.
    
    Classes:
        0: Background
        1: Rock
    """
    
    def __init__(self, root, split='train', transform=None):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        
        # Dataset path
        self.dataset_path = os.path.join(self.root, 'Jiayu')
        
        # Class information
        self.num_classes = 2  # Background + Rock
        self.class_names = ['Background', 'Rock']
        self.ignore_index = 255

        # Get all available samples first
        self.samples = self._get_samples()

        # Auto-detect colors from dataset
        self.background_color, self.rock_color = self._auto_detect_colors()
        
        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()
        
        # Select samples based on split
        if split == 'train':
            self.current_samples = self.train_samples
        elif split == 'val':
            self.current_samples = self.val_samples
        elif split == 'test':
            self.current_samples = self.test_samples
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")
            
        print(f"Jiayu Dataset - {split}: {len(self.current_samples)} samples")

    def _auto_detect_colors(self):
        """
        Automatically detect background and rock colors from the dataset

        Returns:
            tuple: (background_color, rock_color) as RGBA lists
        """
        # Sample a few mask files to analyze colors
        sample_masks = []
        sample_jsons = []

        # Get first 5 samples for color analysis
        for i, sample in enumerate(self.samples[:5]):
            mask_path = sample['mask']
            json_path = sample['metadata']

            if os.path.exists(mask_path) and os.path.exists(json_path):
                sample_masks.append(mask_path)
                sample_jsons.append(json_path)

        if not sample_masks:
            return [0, 0, 0, 255], [0, 255, 54, 255]

        # Method 1: Extract colors from JSON metadata
        detected_colors = self._extract_colors_from_json(sample_jsons)
        if detected_colors:
            return detected_colors

        # Method 2: Analyze mask images directly
        detected_colors = self._analyze_mask_colors(sample_masks)
        if detected_colors:
            return detected_colors

        # Fallback to default colors
        return [0, 0, 0, 255], [0, 255, 54, 255]

    def _extract_colors_from_json(self, json_paths):
        """
        Extract color information from JSON metadata files

        Args:
            json_paths: List of JSON file paths

        Returns:
            tuple: (background_color, rock_color) or None if extraction fails
        """
        try:
            for json_path in json_paths:
                with open(json_path, 'r') as f:
                    data = json.load(f)

                # Navigate to annotations
                captures = data.get('captures', [])
                for capture in captures:
                    annotations = capture.get('annotations', [])
                    for annotation in annotations:
                        if annotation.get('@type') == 'type.unity.com/unity.solo.SemanticSegmentationAnnotation':
                            instances = annotation.get('instances', [])

                            background_color = [0, 0, 0, 255]  # Default background
                            rock_color = None

                            for instance in instances:
                                label_name = instance.get('labelName', '').lower()
                                pixel_value = instance.get('pixelValue', [])

                                if 'rock' in label_name and len(pixel_value) == 4:
                                    rock_color = pixel_value
                                    return background_color, rock_color

            return None

        except Exception as e:
            return None

    def _analyze_mask_colors(self, mask_paths):
        """
        Analyze mask images to detect unique colors

        Args:
            mask_paths: List of mask image file paths

        Returns:
            tuple: (background_color, rock_color) or None if analysis fails
        """
        try:
            all_colors = set()

            for mask_path in mask_paths:
                mask_img = Image.open(mask_path)
                mask_array = np.array(mask_img)

                if len(mask_array.shape) == 3 and mask_array.shape[2] >= 3:
                    # Convert to RGBA if needed
                    if mask_array.shape[2] == 3:
                        # Add alpha channel
                        alpha = np.full((mask_array.shape[0], mask_array.shape[1], 1), 255, dtype=mask_array.dtype)
                        mask_array = np.concatenate([mask_array, alpha], axis=2)

                    # Get unique colors
                    unique_colors = np.unique(mask_array.reshape(-1, 4), axis=0)
                    for color in unique_colors:
                        all_colors.add(tuple(color))

            # Convert to list and analyze
            color_list = list(all_colors)

            if len(color_list) >= 2:
                # Assume the darkest color is background, brightest is rock
                color_brightness = [(sum(c[:3]), c) for c in color_list]
                color_brightness.sort()

                background_color = list(color_brightness[0][1])
                rock_color = list(color_brightness[-1][1])

                return background_color, rock_color

            return None

        except Exception as e:
            return None

    def _get_samples(self):
        """Get all available samples from the dataset directory"""
        samples = []
        
        if not os.path.exists(self.dataset_path):
            raise FileNotFoundError(f"Dataset path not found: {self.dataset_path}")
        
        # Find all step files
        for filename in os.listdir(self.dataset_path):
            if filename.endswith('.camera.png') and not 'semantic' in filename:
                # Extract step number
                step_name = filename.replace('.camera.png', '')
                
                # Check if corresponding files exist
                img_path = os.path.join(self.dataset_path, f"{step_name}.camera.png")
                mask_path = os.path.join(self.dataset_path, f"{step_name}.camera.semantic segmentation.png")
                json_path = os.path.join(self.dataset_path, f"{step_name}.frame_data.json")
                
                if os.path.exists(img_path) and os.path.exists(mask_path) and os.path.exists(json_path):
                    samples.append({
                        'step': step_name,
                        'image': img_path,
                        'mask': mask_path,
                        'metadata': json_path
                    })
        
        # Sort by step number
        samples.sort(key=lambda x: int(x['step'].replace('step', '')))
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")

        return train_samples, val_samples, test_samples
    
    def _convert_mask(self, mask_pil):
        """Convert semantic segmentation mask to class indices

        Args:
            mask_pil: PIL Image of the semantic segmentation mask (RGBA format)

        Returns:
            numpy array with class indices (0: Background, 1: Rock)
        """
        mask_array = np.array(mask_pil)

        # Initialize output mask with background class (0)
        output_mask = np.zeros((mask_array.shape[0], mask_array.shape[1]), dtype=np.uint8)

        # Handle RGBA format (confirmed from analysis)
        if len(mask_array.shape) == 3 and mask_array.shape[2] == 4:
            # Find rock pixels: [0, 255, 54, 255] in RGBA
            rock_pixels = (
                (mask_array[:, :, 0] == self.rock_color[0]) &
                (mask_array[:, :, 1] == self.rock_color[1]) &
                (mask_array[:, :, 2] == self.rock_color[2]) &
                (mask_array[:, :, 3] == self.rock_color[3])
            )
            output_mask[rock_pixels] = 1  # Rock class

            # Optional debug info (disabled by default)
            # if hasattr(self, '_debug_count') and self._debug_count < 3:
            #     unique_colors = np.unique(mask_array.reshape(-1, 4), axis=0)
            #     rock_count = np.sum(rock_pixels)
            #     total_pixels = mask_array.shape[0] * mask_array.shape[1]
            #     print(f"Debug - Unique colors in mask: {unique_colors}")
            #     print(f"Debug - Rock pixels found: {rock_count}/{total_pixels} ({rock_count/total_pixels*100:.2f}%)")
            #     self._debug_count += 1
            # elif not hasattr(self, '_debug_count'):
            #     self._debug_count = 1

            # Verify background pixels (optional check)
            background_pixels = (
                (mask_array[:, :, 0] == self.background_color[0]) &
                (mask_array[:, :, 1] == self.background_color[1]) &
                (mask_array[:, :, 2] == self.background_color[2]) &
                (mask_array[:, :, 3] == self.background_color[3])
            )
            # Background pixels remain 0 (already initialized)

        return output_mask
    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]
        
        # Load image
        image = Image.open(sample['image']).convert('RGB')
        
        # Load and convert mask
        mask_pil = Image.open(sample['mask'])
        mask = self._convert_mask(mask_pil)
        mask = Image.fromarray(mask)
        
        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)
        
        return image, mask
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        return [
            self.background_color[:3],  # Background: RGB only
            self.rock_color[:3],        # Rock: RGB only
        ]

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization

        Args:
            target: numpy array with class indices (0: Background, 1: Rock)

        Returns:
            numpy array with RGB colors for visualization
        """
        # Define color mapping using auto-detected colors
        color_map = np.array([
            self.background_color[:3],  # Background: RGB only
            self.rock_color[:3],        # Rock: RGB only
        ], dtype=np.uint8)

        # Handle invalid class indices
        target = np.clip(target, 0, len(color_map) - 1)

        # Convert to RGB
        rgb_target = color_map[target]

        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt
    
    # Test dataset loading
    dataset_root = '../datasets/data'
    
    for split in ['train', 'val', 'test']:
        try:
            dataset = JiayuDataset(root=dataset_root, split=split)
            print(f"\n{split.upper()} Dataset:")
            print(f"  Samples: {len(dataset)}")
            print(f"  Classes: {dataset.num_classes}")
            print(f"  Class names: {dataset.get_class_names()}")
            
            if len(dataset) > 0:
                # Test loading first sample
                image, mask = dataset[0]
                print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                print(f"  Mask unique values: {np.unique(np.array(mask))}")
                
        except Exception as e:
            print(f"Error loading {split} dataset: {e}")
